#ifndef __MENU_H
#define __MENU_H

// #include <stdio.h>
#include "define.h"

/**
 * @brief 显示系统主菜单
 *
 * 以格式化边框样式输出菜单选项，包含：
 * 1. 健康信息录入
 * 2. 健康数据分析
 * 3. 健康评估报告
 * 4. 数据查询
 * 0. 退出系统
 */
void displayMenu();

/**
 * @brief 安全获取用户菜单选择
 *
 * 获取并验证用户输入，确保为0-4的有效数字。
 * 包含错误处理和输入清理逻辑。
 *
 * @return int 用户选择的有效菜单选项(0-4)
 *
 * @note 会持续提示直到输入有效
 * @warning 输入缓冲区限制为100字符
 */
int getChoice();

bool ask_user_continue(const char *prompt);
#endif