
#ifndef RISK_RESULT_H
#define RISK_RESULT_H

#include "define.h"

/**
 * @brief 心脑血管疾病风险评估 (基于China-PAR模型增强版)
 * @param data 包含评估所需的健康数据
 * @return 风险等级：
 *         - 0: 低危 (<5% 10年风险)
 *         - 1: 中危 (5-9%)
 *         - 2: 高危 (10-19%)
 *         - 3: 极高危 (≥20%)
 */
int assess_cvd_risk(HealthData data);

/**
 * @brief 糖尿病风险评估 (基于ADA标准增强版)
 * @param data 包含评估所需的健康数据
 * @return 风险等级：
 *         - 0: 低危 (<5% 10年风险)
 *         - 1: 中危 (5-19%)
 *         - 2: 高危 (20-49%)
 *         - 3: 极高危 (≥50%)
 */
int assess_diabetes_risk(HealthData data);

/**
 * @brief 慢性肾病风险评估 (基于KDIGO指南增强版)
 * @param data 包含评估所需的健康数据
 * @return 风险等级：
 *         - 0: 正常
 *         - 1: 轻度下降
 *         - 2: 中度下降
 *         - 3: 重度下降
 *         - 4: 肾衰竭
 */
int assess_ckd_risk(HealthData data);

/**
 * @brief 肿瘤风险评估 (基于常见癌症风险因素)
 * @param data 包含评估所需的健康数据
 * @return 风险等级：
 *         - 0: 一般风险
 *         - 1: 中等风险 (建议基础筛查)
 *         - 2: 高风险 (建议专业筛查)
 */
int assess_oncology_risk(HealthData data);

/**
 * @brief 综合慢性病风险评估
 * @param data 包含评估所需的健康数据
 * @return RiskResult结构体，包含所有风险评估结果和建议
 */
RiskResult assess_chronic_disease_risk(HealthData data);

/**
 * @brief 打印风险评估结果（仅打印异常项）
 * @param risk 要打印的风险评估结果指针
 */
void print_risk_assessment(const RiskResult *risk);

#endif // RISK_RESULT_H
