// {
//     "version": "2.0.0",
//     "tasks": [
//         {
//             "label": "Build Project",
//             "type": "shell",
//             "command": "gcc",
//             "args": [
//                 "-g",
//                 "-I${workspaceFolder}/include",
//                 "${workspaceFolder}/Src/main.c",
//                 "${workspaceFolder}/utility/*.c",
//                 "-o",
//                 "${workspaceFolder}/build/program.exe",
//                 "-Wall",
//                 "-Wextra"
//             ],
//             "group": {
//                 "kind": "build",
//                 "isDefault": true
//             },
//             "problemMatcher": [
//                 "$gcc"
//             ],
//             "detail": "Compiler: MinGW GCC",
//             "options": {
//                 "cwd": "${workspaceFolder}"
//             }
//         },
//         {
//             "label": "Clean Build",
//             "type": "shell",
//             "command": "del",
//             "args": [
//                 "/q",
//                 "${workspaceFolder}/build/program.exe"
//             ],
//             "group": "build",
//             "problemMatcher": []
//         }
//     ]
// }
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Build Project",
            "type": "shell",
            "command": "gcc",
            "args": [
                "-g",
                "-I${workspaceFolder}/include",
                "${workspaceFolder}/Src/main.c",
                "${workspaceFolder}/utility/*.c",
                "-o",
                "${workspaceFolder}/build/program.exe",
                "-Wall",
                "-Wextra"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": [
                "$gcc"
            ],
            "detail": "Compiler: MinGW GCC",
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "Clean Build",
            "type": "shell",
            "command": "del",
            "args": [
                "/q",
                "${workspaceFolder}/build/program.exe"
            ],
            "group": "build",
            "problemMatcher": []
        }
    ]
}