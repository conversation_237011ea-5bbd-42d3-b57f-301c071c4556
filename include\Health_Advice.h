
#ifndef __HEALTH_ADVICE_H__
#define __HEALTH_ADVICE_H__

#include "define.h"
#include "Risk_Result.h"
// #include "Health_Data.h"

typedef struct
{
    char weight[256];
    char blood_pressure[256];
    char blood_sugar[256];
    char blood_lipid[256];
    char kidney[256];
    char smoking[256];
    char family[256];
    char inflammation[256];
    char lp_a[256];
    char summary[1024];
} HealthAdvice;

/**
 * @brief 获取BMI分类
 * @param height 身高(m)
 * @param weight 体重(kg)
 * @return BMI分类：
 *         - -1: 数据不完整
 *         - 0: 偏瘦(<18.5)
 *         - 1: 正常(18.5-24)
 *         - 2: 超重(24-28)
 *         - 3: 肥胖(≥28)
 */
int get_bmi_category(float height, float weight);

/**
 * @brief 获取血压分类
 * @param systolic 收缩压(mmHg)
 * @param diastolic 舒张压(mmHg)
 * @return 血压分类：
 *         - -1: 数据不完整
 *         - 0: 正常(<120/80)
 *         - 1: 正常高值(<130/85)
 *         - 2: 临界高血压(<140/90)
 *         - 3: 高血压(≥140/90)
 */
int get_bp_category(float systolic, float diastolic);

/**
 * @brief 计算估算肾小球滤过率(eGFR)
 * @param age 年龄
 * @param gender 性别(1:男, 0:女)
 * @param creatinine 肌酐值(μmol/L)
 * @return 计算出的eGFR值，或NO_INPUT_FLOAT表示数据不完整
 */
float calculate_egfr(int age, int gender, float creatinine);

/**
 * @brief 生成针对性健康建议
 * @param data 健康数据
 * @return HealthAdvice结构体，包含各维度健康建议
 */
HealthAdvice generate_targeted_advice(HealthData data);

/**
 * @brief 打印健康建议
 * @param advice 健康建议指针
 */
void print_health_advice(const HealthAdvice *advice);

/**
 * @brief 生成心脑血管疾病风险建议
 * @param risk_level 风险等级(1-3)
 * @param advice_buffer 建议输出缓冲区
 */
void generate_cvd_advice(int risk_level, char *advice_buffer);

/**
 * @brief 生成糖尿病风险建议
 * @param risk_level 风险等级(1-3)
 * @param advice_buffer 建议输出缓冲区
 */
void generate_diabetes_advice(int risk_level, char *advice_buffer);

/**
 * @brief 生成慢性肾病风险建议
 * @param risk_level 风险等级(1-4)
 * @param advice_buffer 建议输出缓冲区
 */
void generate_ckd_advice(int risk_level, char *advice_buffer);

/**
 * @brief 生成肿瘤风险建议
 * @param risk_level 风险等级(1-2)
 * @param advice_buffer 建议输出缓冲区
 */
void generate_oncology_advice(int risk_level, char *advice_buffer);

/**
 * @brief 生成综合风险建议
 * @param risk 风险评估结果
 * @param advice_buffer 建议输出缓冲区
 */
void generate_risk_advice(const RiskResult *risk, char *advice_buffer);
#endif
