// {
//     "version": "0.2.0",
//     "configurations": [
//         {
//             "name": "(Windows) Debug",
//             "type": "cppdbg",
//             "request": "launch",
//             "program": "${workspaceFolder}/build/program.exe",
//             "args": [],
//             "stopAtEntry": false,
//             "cwd": "${workspaceFolder}",
//             "environment": [],
//             "externalConsole": true, // Windows 建议使用外部终端
//             "MIMode": "gdb",
//             "miDebuggerPath": "C:\\mingw64\\bin\\gdb.exe",
//             "preLaunchTask": "Build Project",
//             "setupCommands": [
//                 {
//                     "description": "Enable pretty-printing",
//                     "text": "-enable-pretty-printing",
//                     "ignoreFailures": true
//                 }
//             ]
//         }
//     ]
// }
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "C++ (GDB/LLDB)",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/program.exe",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}", // 设为项目根目录，即程序运行时从这个目录开始查找文件
            "environment": [],
            "externalConsole": true,
            "MIMode": "gdb",
            "miDebuggerPath": "D:/VS/path/mingw64_C_C++/bin/gdb.exe", //可以在终端使用where gdb查看
            "preLaunchTask": "Build Project",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                }
            ]
        }
    ]
}