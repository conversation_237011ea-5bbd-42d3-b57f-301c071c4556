# 功能2：保存（导出）模块文档

## 1. 功能说明

### 1.1 模块概述

功能2是健康管理系统的保存（导出）模块，主要由`Export_File_Data.c`实现。该模块负责将系统中的健康记录数据安全、可靠地保存到CSV格式的文件中，为数据的持久化存储、备份和跨平台共享提供支持。

### 1.2 核心功能

#### 1.2.1 数据导出功能
- **CSV格式导出**：将健康记录数据导出为标准CSV格式文件，便于在Excel、数据库等工具中使用
- **UTF-8编码支持**：确保中文字符正确显示，支持国际化应用
- **完整字段导出**：导出所有19个健康指标字段，包括基本信息、生理指标、生活习惯等

#### 1.2.2 文件管理功能
- **自动文件命名**：采用时间戳命名方式（YYYYMMDD_HHMMSS.csv），避免文件名冲突
- **目录自动创建**：智能检测并创建保存目录，确保文件能够正常保存
- **路径动态适配**：支持多种目录结构，自动适配不同的运行环境

#### 1.2.3 数据安全功能
- **重入保护**：通过静态标志位防止并发保存操作，确保数据完整性
- **数据验证**：保存前验证数据有效性，确保只保存有意义的健康记录
- **错误处理**：完善的错误处理机制，包括文件创建失败、写入失败等异常情况

#### 1.2.4 用户体验功能
- **智能提示**：根据操作结果提供详细的状态反馈信息
- **增量保存**：只在有新数据时执行保存操作，避免重复保存
- **操作统计**：显示保存成功的记录数量和文件位置信息

### 1.3 技术特点

#### 1.3.1 高可靠性
- **原子性操作**：保存操作要么全部成功，要么全部失败，不会产生不完整的文件
- **异常恢复**：保存失败时自动清理临时文件，保持文件系统整洁
- **状态管理**：通过全局状态标志管理数据变更，避免不必要的I/O操作

#### 1.3.2 跨平台兼容
- **标准C库**：使用标准C库函数，确保跨平台兼容性
- **路径处理**：智能处理不同操作系统的路径分隔符
- **编码统一**：统一使用UTF-8编码，确保数据在不同平台间正确传输

#### 1.3.3 扩展性设计
- **模块化架构**：独立的导出模块，易于维护和扩展
- **格式可扩展**：预留接口支持未来添加其他导出格式（如JSON、XML等）
- **字段可配置**：CSV表头和字段顺序可根据需要调整

### 1.4 应用场景

#### 1.4.1 数据备份
- **定期备份**：系统退出时自动保存，确保数据不丢失
- **手动备份**：用户可随时触发保存操作，创建数据快照
- **版本管理**：时间戳文件名支持保留多个版本的数据备份

#### 1.4.2 数据共享
- **医疗机构共享**：CSV格式便于在不同医疗信息系统间共享数据
- **个人健康档案**：用户可将数据导出用于个人健康管理
- **科研数据支持**：为健康数据分析和科研提供标准化数据源

#### 1.4.3 系统集成
- **第三方工具集成**：CSV格式支持导入Excel、SPSS等分析工具
- **数据库导入**：可直接导入关系型数据库进行进一步处理
- **报表生成**：为生成健康报表提供数据源

## 2. 主要算法

### 2.1 文件保存算法

#### 2.1.1 数据完整性保障算法
```mermaid
flowchart TD
    A[开始保存操作] --> B{检查新数据标志}
    B -->|无新数据| C[提示无需保存]
    B -->|有新数据| D{检查保存进程标志}
    D -->|正在保存| E[提示保存中，忽略请求]
    D -->|未在保存| F[设置保存进程标志]

    F --> G{验证数据有效性}
    G -->|无有效数据| H[清除标志，返回错误]
    G -->|有有效数据| I[确保目录存在]

    I --> J{目录检查结果}
    J -->|目录不存在| K[创建目录]
    J -->|目录存在| L[生成时间戳文件名]
    K --> M{目录创建结果}
    M -->|创建失败| N[清除标志，返回错误]
    M -->|创建成功| L

    L --> O[打开文件写入]
    O --> P{文件打开结果}
    P -->|打开失败| Q[清除标志，返回错误]
    P -->|打开成功| R[写入CSV表头]

    R --> S[逐条写入记录]
    S --> T[统计成功写入数量]
    T --> U[关闭文件]
    U --> V{写入结果检查}
    V -->|全部失败| W[删除空文件]
    V -->|部分或全部成功| X[重置新数据标志]

    W --> Y[清除保存标志]
    X --> Y
    Y --> Z[返回操作结果]

    C --> Z
    E --> Z
    H --> Z
    N --> Z
    Q --> Z
```

数据完整性保障算法是保存模块的核心算法，它确保数据保存操作的原子性和一致性。该算法的核心思想包括：

**状态管理机制**：
通过静态变量`isSavingInProgress`实现保存操作的互斥锁机制，防止多个保存操作同时进行导致数据损坏。同时使用全局变量`hasNewData`跟踪数据变更状态，避免重复保存相同数据。

**分阶段验证策略**：
算法采用多阶段验证策略，在每个关键步骤都进行状态检查。首先验证是否有新数据需要保存，然后检查是否有其他保存操作正在进行，接着验证数据的有效性，最后确保文件系统环境正常。

**错误恢复机制**：
当任何阶段出现错误时，算法会自动清理已分配的资源，重置状态标志，并提供详细的错误信息。对于部分写入失败的情况，会删除不完整的文件，保持文件系统的整洁性。

#### 2.1.2 时间戳文件命名算法

时间戳文件命名算法解决了文件名冲突和版本管理的问题，其核心原理包括：

**高精度时间戳生成**：
算法使用系统当前时间生成精确到秒的时间戳，格式为YYYYMMDD_HHMMSS。这种格式既保证了文件名的唯一性，又便于按时间顺序排列和查找。

**文件名冲突避免**：
由于时间戳精确到秒，在正常使用情况下几乎不可能产生重复的文件名。即使在极端情况下出现冲突，文件系统也会自动处理，不会覆盖已有文件。

**版本历史管理**：
时间戳命名方式天然支持版本历史管理，用户可以通过文件名直观地了解数据保存的时间，便于进行数据回溯和版本比较。

#### 2.1.3 CSV格式化算法

CSV格式化算法负责将结构化的健康记录数据转换为标准CSV格式，其主要特点包括：

**字段顺序标准化**：
算法按照预定义的字段顺序输出数据，确保生成的CSV文件具有一致的结构。字段顺序从基本信息（日期、姓名、年龄、性别）开始，依次包含身体指标、生理参数、生活习惯和特殊指标。

**数据类型处理**：
针对不同的数据类型采用相应的格式化策略。整数类型直接输出，浮点数保留一位小数，字符串类型确保不包含CSV分隔符，布尔类型转换为0/1数值表示。

**编码兼容性**：
算法确保输出的CSV文件使用UTF-8编码，支持中文字符的正确显示。同时遵循CSV标准，使用逗号作为字段分隔符，换行符作为记录分隔符。

### 2.2 目录管理算法

#### 2.2.1 动态路径检测算法
```mermaid
flowchart TD
    A[开始路径检测] --> B[尝试打开上级目录]
    B --> C{上级目录存在?}
    C -->|存在| D[返回上级目录路径]
    C -->|不存在| E[尝试打开当前目录]
    E --> F{当前目录存在?}
    F -->|存在| G[返回当前目录路径]
    F -->|不存在| H[返回NULL]

    D --> I[路径检测完成]
    G --> I
    H --> I
```

动态路径检测算法解决了不同运行环境下的路径适配问题，其核心机制包括：

**多级路径尝试策略**：
算法首先尝试访问上一级目录的File_Save文件夹（../File_Save），这适用于程序在build或bin子目录中运行的情况。如果上级目录不存在，则尝试当前目录的File_Save文件夹，适用于程序在项目根目录运行的情况。

**实时目录验证**：
算法通过实际尝试打开目录的方式验证路径有效性，而不是简单的字符串拼接。这种方式能够准确反映文件系统的实际状态，避免因路径错误导致的保存失败。

**环境自适应机制**：
算法能够自动适应不同的项目结构和运行环境，无需手动配置路径。这种设计提高了系统的可移植性和用户友好性。

#### 2.2.2 目录创建算法

目录创建算法确保保存目录在需要时能够自动创建，其实现策略包括：

**存在性检查机制**：
在尝试创建目录之前，算法首先使用stat函数检查目录是否已存在。只有当目录不存在时才执行创建操作，避免不必要的系统调用。

**权限处理策略**：
算法使用系统默认权限创建目录，确保创建的目录具有适当的读写权限。同时处理权限不足等异常情况，提供相应的错误提示。

**递归创建支持**：
虽然当前实现只创建单级目录，但算法设计预留了递归创建多级目录的扩展空间，便于未来功能升级。

### 2.3 数据验证算法

#### 2.3.1 记录有效性验证算法

记录有效性验证算法确保只有有意义的数据被保存到文件中，其验证策略包括：

**最新记录检查**：
算法通过getLatestRecord函数获取最新的健康记录，验证记录数组不为空且包含有效数据。这种检查确保保存操作有实际的数据内容。

**数据完整性验证**：
算法检查关键字段的完整性，确保必要的健康指标数据存在。虽然系统允许某些字段为空（使用NO_INPUT_FLOAT标记），但核心字段如日期、姓名等必须有效。

**逻辑一致性检查**：
算法验证数据的逻辑一致性，如年龄范围、生理指标的合理性等。这种验证有助于发现数据录入错误，提高数据质量。

#### 2.3.2 写入结果验证算法

写入结果验证算法确保数据成功写入文件，其验证机制包括：

**逐条写入验证**：
算法对每条记录的写入操作进行验证，通过fprintf函数的返回值判断写入是否成功。只有成功写入的记录才会被计入成功计数。

**整体结果评估**：
算法统计成功写入的记录数量，与总记录数进行比较。如果所有记录都写入失败，会删除空文件；如果部分成功，会报告具体的成功数量。

**文件完整性保障**：
算法确保文件在写入完成后正确关闭，避免数据丢失。同时在写入失败时清理不完整的文件，保持文件系统整洁。

### 2.4 错误处理算法

#### 2.4.1 分级错误处理算法

分级错误处理算法根据错误的严重程度采用不同的处理策略：

**致命错误处理**：
对于无法恢复的错误（如磁盘空间不足、权限不够等），算法会立即终止保存操作，清理已分配的资源，并向用户报告详细的错误信息。

**警告级错误处理**：
对于可以继续执行的错误（如个别记录格式异常），算法会记录错误信息，跳过问题记录，继续处理其他有效记录。

**信息级提示处理**：
对于正常的状态信息（如没有新数据需要保存），算法会提供友好的提示信息，不会中断用户操作流程。

#### 2.4.2 资源清理算法

资源清理算法确保在任何情况下都不会发生资源泄漏：

**自动资源管理**：
算法采用RAII（Resource Acquisition Is Initialization）思想，在函数开始时获取资源，在函数结束前确保资源被正确释放。

**异常安全保证**：
即使在发生异常的情况下，算法也能保证文件句柄被正确关闭，内存被正确释放，状态标志被正确重置。

**清理验证机制**：
算法在清理资源后会验证清理操作的有效性，确保系统状态恢复到操作前的状态。

## 3. 代码展示

### 3.1 主要导出函数

#### 3.1.1 saveToFile函数 - 核心保存接口

```c
/**
 * 保存健康记录到CSV文件
 * 功能：将当前系统中的健康记录数据导出为CSV格式文件
 * 特点：线程安全、自动命名、完整错误处理
 * 返回：无返回值，通过控制台输出操作结果
 */
void saveToFile() {
    // 静态变量实现线程安全，防止并发保存操作
    static int isSavingInProgress = 0;

    // 检查是否有新数据需要保存，避免重复保存
    if (!hasNewData) {
        printf("没有新数据需要保存。\n");
        return;
    }

    // 检查是否已有保存操作在进行，实现互斥锁机制
    if (isSavingInProgress) {
        printf("正在保存中，请稍候...\n");
        return;
    }

    // 设置保存进程标志，标记开始保存操作
    isSavingInProgress = 1;

    // 获取最新的健康记录数据，验证数据有效性
    HealthRecord* records = getLatestRecord();
    if (records == NULL) {
        printf("没有有效的健康记录可以保存。\n");
        isSavingInProgress = 0;  // 清除保存标志
        return;
    }

    // 获取保存目录路径，支持多种目录结构
    char* saveDir = getSaveDir();
    if (saveDir == NULL) {
        printf("无法确定保存目录。\n");
        isSavingInProgress = 0;  // 清除保存标志
        return;
    }

    // 确保保存目录存在，不存在则创建
    struct stat st = {0};
    if (stat(saveDir, &st) == -1) {
        // 目录不存在，尝试创建
        if (mkdir(saveDir) != 0) {
            printf("无法创建保存目录：%s\n", saveDir);
            isSavingInProgress = 0;  // 清除保存标志
            return;
        }
    }

    // 生成基于时间戳的文件名，格式：YYYYMMDD_HHMMSS.csv
    time_t now;
    time(&now);
    struct tm* timeinfo = localtime(&now);

    char filename[256];
    snprintf(filename, sizeof(filename), "%s/%04d%02d%02d_%02d%02d%02d.csv",
             saveDir,
             timeinfo->tm_year + 1900,  // 年份（从1900开始计算）
             timeinfo->tm_mon + 1,      // 月份（从0开始，需要+1）
             timeinfo->tm_mday,         // 日期
             timeinfo->tm_hour,         // 小时
             timeinfo->tm_min,          // 分钟
             timeinfo->tm_sec);         // 秒

    // 打开文件进行写入，使用写入模式创建新文件
    FILE* file = fopen(filename, "w");
    if (file == NULL) {
        printf("无法创建文件：%s\n", filename);
        isSavingInProgress = 0;  // 清除保存标志
        return;
    }

    // 写入CSV文件头，定义各列的含义
    fprintf(file, "日期,姓名,年龄,性别,身高,体重,BMI,收缩压,舒张压,心率,体温,血糖,胆固醇,血红蛋白,白细胞计数,运动频率,睡眠时间,吸烟状况,饮酒状况\n");

    // 逐条写入健康记录，统计成功写入的数量
    int successCount = 0;
    for (int i = 0; i < MAX_RECORDS && records[i].date[0] != '\0'; i++) {
        // 写入每条记录的所有字段，使用逗号分隔
        int result = fprintf(file, "%s,%s,%d,%s,%.1f,%.1f,%.1f,%.1f,%.1f,%.1f,%.1f,%.1f,%.1f,%.1f,%.1f,%d,%.1f,%d,%d\n",
                           records[i].date,           // 日期
                           records[i].name,           // 姓名
                           records[i].age,            // 年龄
                           records[i].gender,         // 性别
                           records[i].height,         // 身高
                           records[i].weight,         // 体重
                           records[i].bmi,            // BMI指数
                           records[i].systolic_bp,    // 收缩压
                           records[i].diastolic_bp,   // 舒张压
                           records[i].heart_rate,     // 心率
                           records[i].temperature,    // 体温
                           records[i].blood_sugar,    // 血糖
                           records[i].cholesterol,    // 胆固醇
                           records[i].hemoglobin,     // 血红蛋白
                           records[i].white_blood_cell, // 白细胞计数
                           records[i].exercise_frequency, // 运动频率
                           records[i].sleep_hours,    // 睡眠时间
                           records[i].smoking,        // 吸烟状况
                           records[i].drinking);      // 饮酒状况

        // 检查写入是否成功，统计成功数量
        if (result > 0) {
            successCount++;
        }
    }

    // 关闭文件，确保数据写入磁盘
    fclose(file);

    // 根据写入结果提供反馈信息
    if (successCount == 0) {
        // 如果没有成功写入任何记录，删除空文件
        remove(filename);
        printf("保存失败：没有有效数据写入。\n");
    } else {
        // 保存成功，重置新数据标志，显示成功信息
        hasNewData = 0;
        printf("成功保存 %d 条健康记录到文件：%s\n", successCount, filename);
    }

    // 清除保存进程标志，允许下次保存操作
    isSavingInProgress = 0;
}
```

### 3.2 辅助函数

#### 3.2.1 getSaveDir函数 - 动态路径检测

```c
/**
 * 获取保存目录路径
 * 功能：动态检测并返回合适的文件保存目录
 * 策略：优先使用上级目录，其次使用当前目录
 * 返回：成功返回目录路径字符串，失败返回NULL
 */
char* getSaveDir() {
    // 静态变量存储目录路径，避免重复检测
    static char saveDir[256] = {0};

    // 如果已经检测过路径，直接返回缓存结果
    if (saveDir[0] != '\0') {
        return saveDir;
    }

    // 首先尝试上级目录的File_Save文件夹
    // 适用于程序在build或bin子目录中运行的情况
    DIR* dir = opendir("../File_Save");
    if (dir != NULL) {
        // 上级目录存在，使用该路径
        closedir(dir);
        strcpy(saveDir, "../File_Save");
        return saveDir;
    }

    // 上级目录不存在，尝试当前目录的File_Save文件夹
    // 适用于程序在项目根目录运行的情况
    dir = opendir("File_Save");
    if (dir != NULL) {
        // 当前目录存在，使用该路径
        closedir(dir);
        strcpy(saveDir, "File_Save");
        return saveDir;
    }

    // 两个路径都不存在，返回NULL表示无法确定保存目录
    return NULL;
}
```

#### 3.2.2 getLatestRecord函数 - 数据获取接口

```c
/**
 * 获取最新的健康记录数据
 * 功能：从全局数据存储中获取当前的健康记录数组
 * 验证：检查数据有效性，确保返回的数据可用于保存
 * 返回：成功返回记录数组指针，失败返回NULL
 */
HealthRecord* getLatestRecord() {
    // 检查全局健康记录数组是否已初始化
    if (healthRecords == NULL) {
        return NULL;
    }

    // 检查是否至少有一条有效记录
    // 通过检查第一条记录的日期字段判断数据有效性
    if (healthRecords[0].date[0] == '\0') {
        return NULL;  // 没有有效数据
    }

    // 返回健康记录数组的指针
    return healthRecords;
}
```

### 3.3 数据结构定义

#### 3.3.1 HealthRecord结构体 - 健康记录数据结构

```c
/**
 * 健康记录数据结构
 * 功能：存储完整的个人健康信息
 * 字段：包含19个健康相关字段，涵盖基本信息、生理指标、生活习惯
 * 用途：作为系统中健康数据的标准存储格式
 */
typedef struct {
    char date[20];              // 记录日期，格式：YYYY-MM-DD
    char name[50];              // 姓名，最大50个字符
    int age;                    // 年龄，整数值
    char gender[10];            // 性别，如"男"、"女"
    float height;               // 身高，单位：厘米
    float weight;               // 体重，单位：公斤
    float bmi;                  // BMI指数，体重指数
    float systolic_bp;          // 收缩压，单位：mmHg
    float diastolic_bp;         // 舒张压，单位：mmHg
    float heart_rate;           // 心率，单位：次/分钟
    float temperature;          // 体温，单位：摄氏度
    float blood_sugar;          // 血糖，单位：mmol/L
    float cholesterol;          // 胆固醇，单位：mmol/L
    float hemoglobin;           // 血红蛋白，单位：g/L
    float white_blood_cell;     // 白细胞计数，单位：10^9/L
    int exercise_frequency;     // 运动频率，单位：次/周
    float sleep_hours;          // 睡眠时间，单位：小时/天
    int smoking;                // 吸烟状况，0=不吸烟，1=吸烟
    int drinking;               // 饮酒状况，0=不饮酒，1=饮酒
} HealthRecord;
```

### 3.4 全局变量和常量定义

#### 3.4.1 全局状态变量

```c
/**
 * 全局健康记录数组
 * 功能：存储系统中所有的健康记录数据
 * 类型：HealthRecord数组指针
 * 作用域：整个程序，由其他模块负责初始化和维护
 */
extern HealthRecord* healthRecords;

/**
 * 新数据标志
 * 功能：标识是否有新的健康数据需要保存
 * 类型：整数，0表示无新数据，非0表示有新数据
 * 用途：避免重复保存相同的数据，提高效率
 */
extern int hasNewData;

/**
 * 最大记录数常量
 * 功能：定义系统支持的最大健康记录数量
 * 值：通常设置为合理的上限，如1000条记录
 * 用途：数组边界检查，防止内存越界访问
 */
#define MAX_RECORDS 1000

/**
 * 无输入浮点数标记
 * 功能：标识浮点数字段没有输入值的特殊标记
 * 值：通常使用-1.0或其他不可能的健康指标值
 * 用途：区分真实的0值和未输入状态
 */
#define NO_INPUT_FLOAT -1.0f
```

### 3.5 头文件包含

#### 3.5.1 系统头文件

```c
/**
 * 标准输入输出库
 * 功能：提供printf、fprintf、fopen、fclose等文件操作函数
 * 用途：控制台输出和文件读写操作
 */
#include <stdio.h>

/**
 * 标准库函数
 * 功能：提供内存管理、字符串处理、系统调用等基础函数
 * 用途：malloc、free、strcpy、snprintf等操作
 */
#include <stdlib.h>

/**
 * 字符串处理库
 * 功能：提供字符串操作相关函数
 * 用途：strcpy、strlen、strcmp等字符串操作
 */
#include <string.h>

/**
 * 时间处理库
 * 功能：提供时间获取和格式化函数
 * 用途：time、localtime等时间戳生成操作
 */
#include <time.h>

/**
 * 文件状态库
 * 功能：提供文件和目录状态检查函数
 * 用途：stat、mkdir等文件系统操作
 */
#include <sys/stat.h>

/**
 * 目录操作库
 * 功能：提供目录打开、读取、关闭等操作
 * 用途：opendir、closedir等目录管理操作
 */
#include <dirent.h>
```

#### 3.5.2 项目头文件

```c
/**
 * 导出功能头文件
 * 功能：声明saveToFile函数和相关接口
 * 内容：函数原型、常量定义、使用说明
 */
#include "Export_File_Data.h"

/**
 * 数据导入头文件
 * 功能：定义HealthRecord结构体和相关数据类型
 * 内容：数据结构定义、字段说明、类型定义
 */
#include "Import_Data.h"

/**
 * 路径检查头文件
 * 功能：声明getSaveDir等路径管理函数
 * 内容：路径检测、目录管理相关函数声明
 */
#include "Get_Check_Path.h"

/**
 * 数据转换头文件
 * 功能：声明getLatestRecord等数据访问函数
 * 内容：数据获取、格式转换相关函数声明
 */
#include "Convert_Data.h"
```

### 3.6 使用示例

#### 3.6.1 基本使用方法

```c
/**
 * 保存健康记录的基本使用示例
 * 场景：用户输入完健康数据后，保存到文件
 */
void example_basic_usage() {
    // 假设已经有健康记录数据，并且hasNewData标志已设置

    // 直接调用保存函数，无需传递参数
    saveToFile();

    // 函数会自动处理以下操作：
    // 1. 检查是否有新数据需要保存
    // 2. 验证数据有效性
    // 3. 确定保存目录
    // 4. 生成时间戳文件名
    // 5. 写入CSV格式数据
    // 6. 提供操作结果反馈
}
```

#### 3.6.2 程序退出时自动保存

```c
/**
 * 程序退出时自动保存示例
 * 场景：确保程序关闭前数据不丢失
 */
void example_auto_save_on_exit() {
    // 注册退出处理函数
    atexit(saveToFile);

    // 或者在main函数结束前手动调用
    // 检查是否有未保存的数据
    if (hasNewData) {
        printf("检测到未保存的数据，正在自动保存...\n");
        saveToFile();
    }
}
```

#### 3.6.3 错误处理示例

```c
/**
 * 错误处理和状态检查示例
 * 场景：需要了解保存操作的详细状态
 */
void example_error_handling() {
    // 保存前检查系统状态
    if (healthRecords == NULL) {
        printf("错误：健康记录数据未初始化\n");
        return;
    }

    if (!hasNewData) {
        printf("提示：当前没有新数据需要保存\n");
        return;
    }

    // 执行保存操作
    printf("开始保存健康记录...\n");
    saveToFile();

    // 检查保存结果（通过hasNewData标志）
    if (!hasNewData) {
        printf("保存操作完成，数据已成功写入文件\n");
    } else {
        printf("保存操作可能未完全成功，请检查错误信息\n");
    }
}
```

### 3.7 编译和链接

#### 3.7.1 编译命令

```bash
# 编译Export_File_Data.c模块
gcc -c utility/Export_File_Data.c -o Export_File_Data.o -I include/

# 编译相关依赖模块
gcc -c utility/Get_Check_Path.c -o Get_Check_Path.o -I include/
gcc -c utility/Convert_Data.c -o Convert_Data.o -I include/

# 链接生成可执行文件
gcc Export_File_Data.o Get_Check_Path.o Convert_Data.o main.o -o health_system
```

#### 3.7.2 Makefile配置

```makefile
# 编译器设置
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -I include/

# 目标文件
OBJS = Export_File_Data.o Get_Check_Path.o Convert_Data.o main.o

# 主目标
health_system: $(OBJS)
	$(CC) $(OBJS) -o health_system

# 导出模块编译规则
Export_File_Data.o: utility/Export_File_Data.c include/Export_File_Data.h
	$(CC) $(CFLAGS) -c utility/Export_File_Data.c

# 清理规则
clean:
	rm -f *.o health_system
```

---

## 总结

功能2（保存/导出模块）是健康管理系统的重要组成部分，它提供了完整的数据持久化解决方案。该模块通过精心设计的算法和完善的错误处理机制，确保健康数据能够安全、可靠地保存到CSV格式文件中。

**主要特点**：
- **线程安全**：通过静态标志位防止并发操作
- **自动化程度高**：自动检测路径、生成文件名、创建目录
- **错误处理完善**：多级错误处理，确保系统稳定性
- **跨平台兼容**：使用标准C库，支持多种操作系统
- **用户友好**：详细的状态反馈和操作提示

该模块为健康管理系统提供了可靠的数据导出功能，支持数据备份、共享和进一步分析处理。
```
```