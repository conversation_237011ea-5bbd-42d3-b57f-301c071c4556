
#include "define.h"

void loadFromFile(const char *targetDate)
{
    extern HealthRecord records[MAX_ENTRIES];
    extern int numRecords;
    numRecords = 0; // 重置记录数

    DIR *dir;
    struct dirent *ent;
    struct stat fileStat;
    int errorCount = 0;
    int duplicateCount = 0;

    // 1. 检查目录是否存在
    if ((dir = opendir(getSaveDir())) == NULL)
    {
        printf("[错误] 无法打开目录 %s: %s\n", getSaveDir(), strerror(errno));
        return;
    }

    // 2. 遍历目录中的文件
    while ((ent = readdir(dir)) != NULL && numRecords < MAX_ENTRIES)
    {
        char fullpath[MAX_PATH];
        snprintf(fullpath, MAX_PATH, "%s/%s", getSaveDir(), ent->d_name);

        // 跳过非CSV文件
        if (stat(fullpath, &fileStat) != 0 || !S_ISREG(fileStat.st_mode) ||
            strstr(ent->d_name, ".csv") == NULL)
        {
            continue;
        }

        // 3. 检查文件名是否匹配目标日期（如果指定）
        char fileDate[11] = {0};
        if (strlen(ent->d_name) >= 10)
        {
            strncpy(fileDate, ent->d_name, 10);
            fileDate[4] = '-'; // 格式化为 YYYY-MM-DD
            fileDate[7] = '-';
        }

        if (targetDate != NULL && strncmp(fileDate, targetDate, 10) != 0)
        {
            continue;
        }

        // 4. 打开文件并解析内容
        FILE *file = fopen(fullpath, "r");
        if (!file)
        {
            printf("[警告] 无法打开文件 %s: %s\n", fullpath, strerror(errno));
            continue;
        }

        // 跳过表头
        char line[1024];
        if (!fgets(line, sizeof(line), file))
        {
            fclose(file);
            continue;
        }

        // 逐行读取记录
        while (fgets(line, sizeof(line), file) != NULL && numRecords < MAX_ENTRIES)
        {
            HealthRecord record;
            memset(&record, 0, sizeof(HealthRecord));

            // 解析CSV行
            int fields = sscanf(line, "%19[^,],%20[^,],%d,%d,%f,%f,%f,%f,%f,%f,%f,%f,%f,%d,%f,%d,%d,%f,%f",
                                record.date, record.name, &record.age, &record.gender, &record.height,
                                &record.weight, &record.systolic_bp, &record.diastolic_bp,
                                &record.fasting_glucose, &record.hba1c, &record.cholesterol,
                                &record.ldl, &record.creatinine, &record.heartRate,
                                &record.sleepHours, &record.is_smoker, &record.family_history,
                                &record.lp_a, &record.hs_crp);

            if (fields != 19)
            {
                errorCount++;
                continue;
            }

            // 检查是否为重复记录（仅当targetDate为NULL时）
            if (targetDate == NULL)
            {
                int isDuplicate = 0;
                for (int i = 0; i < numRecords; i++)
                {
                    // 比较所有字段是否相同
                    if (strcmp(records[i].date, record.date) == 0 &&
                        strcmp(records[i].name, record.name) == 0 &&
                        records[i].age == record.age &&
                        records[i].gender == record.gender &&
                        records[i].height == record.height &&
                        records[i].weight == record.weight &&
                        records[i].systolic_bp == record.systolic_bp &&
                        records[i].diastolic_bp == record.diastolic_bp &&
                        records[i].fasting_glucose == record.fasting_glucose &&
                        records[i].hba1c == record.hba1c &&
                        records[i].cholesterol == record.cholesterol &&
                        records[i].ldl == record.ldl &&
                        records[i].creatinine == record.creatinine &&
                        records[i].heartRate == record.heartRate &&
                        records[i].sleepHours == record.sleepHours &&
                        records[i].is_smoker == record.is_smoker &&
                        records[i].family_history == record.family_history &&
                        records[i].lp_a == record.lp_a &&
                        records[i].hs_crp == record.hs_crp)
                    {
                        isDuplicate = 1;
                        duplicateCount++;
                        break;
                    }
                }

                if (isDuplicate)
                {
                    continue; // 跳过重复记录
                }
            }

            // 更新记录数组
            records[numRecords++] = record;
        }
        fclose(file);
    }
    closedir(dir);

    // 5. 输出结果
    HealthRecord *latest = getLatestRecord(records, numRecords);
    if (latest == NULL)
    {
        printf("[系统] 未找到匹配的健康记录\n");
    }
    else
    {
        printf("[成功] 加载 %d 条记录，最新日期: %s\n", numRecords, latest->date);
    }

    if (errorCount > 0)
    {
        printf("[警告] 跳过 %d 条格式错误的记录\n", errorCount);
    }

    if (duplicateCount > 0 && targetDate == NULL)
    {
        printf("[提示] 跳过 %d 条重复记录\n", duplicateCount);
    }
}

void printLoadedData()
{
    extern HealthRecord records[MAX_ENTRIES];
    extern int numRecords;

    HealthRecord *latest = getLatestRecord(records, numRecords);

    if (latest == NULL)
    {
        printf("\n──────────────────────────────────\n");
        printf("[系统] 当前没有可显示的健康记录\n");
        printf("──────────────────────────────────\n");
        return;
    }

    printf("\n════════════ 最新健康记录 ════════════\n");
    printf("记录日期: %s\n", latest->date);
    printf("══════════════════════════════════════\n");

    // 1. 基本信息
    printf("[基本信息]\n");
    printf("  年龄: %d岁     性别: %s\n",
           latest->age, latest->gender == 0 ? "女" : "男");

    float bmi = latest->weight / ((latest->height / 100) * (latest->height / 100));
    printf("  身高: %.1fcm   体重: %.1fkg   BMI: %.1f",
           latest->height, latest->weight, bmi);

    // BMI分类
    if (bmi < 18.5)
        printf(" (偏轻)");
    else if (bmi < 24)
        printf(" (正常)");
    else if (bmi < 28)
        printf(" (超重)");
    else
        printf(" (肥胖)");
    printf("\n\n");

    // 2. 健康指标
    printf("[健康指标]\n");
    printf("  血压: %.0f/%.0f mmHg   ", latest->systolic_bp, latest->diastolic_bp);

    // 血压分级
    if (latest->systolic_bp < 90 || latest->diastolic_bp < 60)
        printf("(偏低)");
    else if (latest->systolic_bp < 120 && latest->diastolic_bp < 80)
        printf("(正常)");
    else if (latest->systolic_bp < 140 || latest->diastolic_bp < 90)
        printf("(偏高)");
    else
        printf("(危险)");

    printf("\n  空腹血糖: %.1f mmol/L   HbA1c: %.1f%%\n",
           latest->fasting_glucose, latest->hba1c);
    printf("  总胆固醇: %.1f mmol/L   LDL: %.1f mmol/L\n",
           latest->cholesterol, latest->ldl);
    printf("  肌酐: %.1f μmol/L   心率: %d 次/分钟\n",
           latest->creatinine, latest->heartRate);
    printf("\n");

    // 3. 生活习惯
    printf("[生活习惯]\n");
    printf("  睡眠: %.1f 小时   吸烟: %s   家族史: %s\n",
           latest->sleepHours,
           latest->is_smoker ? "是" : "否",
           latest->family_history ? "有" : "无");
    printf("\n");

    // 4. 特殊指标
    printf("[特殊指标]\n");
    printf("  脂蛋白(a): %.1f mmol/L   hs-CRP: %.1f mg/L\n",
           latest->lp_a, latest->hs_crp);

    printf("\n════════════ 记录统计 ════════════\n");
    printf("当前共加载 %d 条健康记录\n", numRecords);
    printf("══════════════════════════════════\n");
}